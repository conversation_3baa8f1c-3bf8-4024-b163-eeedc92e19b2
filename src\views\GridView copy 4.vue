<template>
  <!-- class="hover-card" 动画效果  -->
  <div class="dashboard-container">

    <div class="side-panel left-content2">
      <div class="panel-content">
        <TitleComponent
          title="项目概览"
          subtitle="Project Overview"
          @click="handleWechatLogin">
          <project-overview />
        </TitleComponent>

        <TitleComponent title="设备概览" subtitle="Equipment Overview" showSelect="true"
          v-model="DayDimension">
          <device-overview />
        </TitleComponent>

        <TitleComponent title="室内环境监测" subtitle="Indoor  Environment">
          <indoor-environment />
        </TitleComponent>
      </div>
    </div>
    <!-- 左侧面板 -->
    <div class="side-panel left-content">
      <div class="panel-content">
        <TitleComponent
          title="项目概览"
          subtitle="Project Overview"
          @click="handleWechatLogin">
          <project-overview />
        </TitleComponent>

        <TitleComponent title="设备概览" subtitle="Equipment Overview" showSelect="true"
          v-model="DayDimension">
          <device-overview />
        </TitleComponent>

        <TitleComponent title="室内环境监测" subtitle="Indoor  Environment">
          <indoor-environment />
        </TitleComponent>
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="side-panel right-content">
      <div class="panel-content">
        <TitleComponent title="能源统计" subtitle="Energy Statistics" showSelect="true" :dimensions="dimensions"
          v-model="energyTimeDimension">
          <carbon-reduction-stats @openSceneDetail="handleOpenSceneDetail" :timeDimension="energyTimeDimension" />
        </TitleComponent>
        <TitleComponent title="碳排放统计" subtitle="Carbon Emission" showSelect="true" :dimensions="dimensions"
          v-model="emissionTimeDimension">
          <carbon-total-stats :timeDimension="emissionTimeDimension" />
        </TitleComponent>

        <TitleComponent title="碳中和统计" subtitle="Statistics" showSelect="true"
          v-model="behaviorTimeDimension">
          <carbon-operation-stats @openSceneDetail="handleOpenSceneDetail" :timeDimension="behaviorTimeDimension"
            :selectedBuilding="selectedBuilding" />
        </TitleComponent>
      </div>
    </div>
  </div>
  <Dialog v-model:visible="dialogVisible" title="碳访西伏河">
    <div class="dialog-content">
      <iframe src="https://sjcj.gxaqhbzx.com:8763/#/" class="iframe-content" frameborder="0"></iframe>
    </div>

  </Dialog>
</template>

<script setup>

import TitleComponent from '../components/common/TitleComponent.vue'
import ProjectOverview from '../components/statistics/ProjectOverview.vue'
import DeviceOverview from '../components/statistics/DeviceOverview.vue'
import IndoorEnvironment from '../components/statistics/IndoorEnvironment.vue'
import CarbonTotalStats from '../components/statistics/CarbonTotalStats.vue'

import CarbonReductionStats from '../components/statistics/CarbonReductionStats.vue'
import CarbonOperationStats from '../components/statistics/CarbonOperationStats.vue'
import Dialog from '../components/common/Dialog.vue'



import { ref, watch } from 'vue'
const handleWechatLogin = () => {
  console.log('2112')

}

const handleCloseDialog = () => {
  dialogVisible.value = false

  console.log("关闭弹窗", "UE", "TanChuang", "Close");
}




const jianpaidimensions = ref([
  { label: '行为碳', value: 'behavior' },
])

const dimensions = [
  { label: '本日', value: 'day' },
  { label: '本月', value: 'month' },
  { label: '本年', value: 'year' }
]
const DayDimension = ref('day')
const selectedBuilding = ref('A1#')
const energyTimeDimension = ref('day')
const emissionTimeDimension = ref('day')

// 监听维度变化
watch(() => DayDimension.value, (newDimension) => {
  console.log('时间维度变化:', newDimension)
})

// 监听楼宇变化
watch(() => selectedBuilding.value, (newBuilding) => {
  console.log('选中楼宇变化:', newBuilding)
})

// 添加状态管理

const currentEchartsDimension = ref('month')
const currentDimension = ref('direct')
const currentJianpaiDimension = ref('project')

const emit = defineEmits(['switchView'])

const handleJumpToDashboard = () => {
  emit('switchView', 'dashboard')
}

const dialogVisible = ref(false)

const handleOpenSceneDetail = () => {
  dialogVisible.value = true
}

const behaviorTimeDimension = ref('day')

</script>

<style lang="less" scoped>
.dashboard-container {
  margin-top: 26px;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none; // 不阻挡Three.js交互

  --side-panel-width: 436px;
  --side-panel-margin: 24px;

  .side-panel {
    position: absolute;
    width: var(--side-panel-width);
    z-index: 10;
    pointer-events: auto; // 侧边面板需要交互

    .panel-content {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 20px;
      height: 100%;
      pointer-events: auto; // 面板内容需要交互
    }
  }

  .left-content {
    left: 6604px;
  }
  .left-content2 {
    left: 6110px;
  }
  .right-content {
    right: var(--side-panel-margin);
  }




}



#close-btn {
  position: absolute;
  top: 15px !important;
  right: 22px !important;
  width: 25px !important;
  height: 25px !important;
  cursor: pointer;
}

.dialog-content {
  font-size: 33px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: PangMenZhengDao;
  color: black;
  flex-direction: column;
  background-color: #fff !important;
  height: 908px;

  .iframe-content {
    width: 100%;
    height: 100%;
  }
}
</style>
